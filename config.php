<?php
// Configuración general del sitio
define('SITE_NAME', 'Escala Libre');
define('SITE_TAGLINE', 'Aprender con sentido, crecer en libertad');
define('SITE_URL', 'https://escalalibre.pe');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+51 994 953 176');

// Colores del brand
define('COLOR_VERDE_PETROLEO', '#007C84');
define('COLOR_AMARILLO_MOSTAZA', '#F4C843');
define('COLOR_TURQUESA_CLARO', '#00B1AE');
define('COLOR_MAGENTA_OSCURO', '#C03671');
define('COLOR_VERDE_OSCURO', '#005D64');
define('COLOR_BLANCO_CREMA', '#FAF8F4');

// Configuración de la base de datos (para futuro uso)
/*
define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
define('DB_NAME', $_ENV['DB_NAME'] ?? 'escalalibre_db');
define('DB_USER', $_ENV['DB_USER'] ?? 'root');
define('DB_PASS', $_ENV['DB_PASS'] ?? '');
*/

// Configuración de desarrollo/producción
define('ENVIRONMENT', $_ENV['ENVIRONMENT'] ?? 'development');
define('DEBUG', ENVIRONMENT === 'development');

// Configuración de rutas
define('BASE_PATH', dirname(__FILE__));
define('ASSETS_PATH', '/assets');
define('IMAGES_PATH', ASSETS_PATH . '/images');
define('CSS_PATH', ASSETS_PATH . '/css');
define('JS_PATH', ASSETS_PATH . '/js');

// Función para incluir archivos de manera segura
function include_component($component_name) {
    $file_path = BASE_PATH . '/components/' . $component_name . '.php';
    if (file_exists($file_path)) {
        include $file_path;
    } else {
        if (DEBUG) {
            echo "<!-- Component not found: $component_name -->";
        }
    }
}

// Función para generar URLs limpias
function url($path = '') {
    return rtrim(SITE_URL, '/') . '/' . ltrim($path, '/');
}

// Función para assets
function asset($path) {
    return ASSETS_PATH . '/' . ltrim($path, '/');
}
?>
