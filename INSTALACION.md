# 🚀 Guía de Instalación - Escala Libre

## Paso 1: Preparar el Servidor

### Requisitos mínimos:
- **PHP**: 7.4 o superior
- **Apache**: con mod_rewrite habilitado
- **Función mail()**: habilitada para formulario de contacto
- **Espacio**: 50MB mínimo

## Paso 2: Subir Archivos

1. **Comprimir el proyecto** en tu computadora
2. **Acceder a cPanel** de tu hosting
3. **Ir a File Manager** (Administrador de archivos)
4. **Navegar a public_html** (o la carpeta de tu dominio)
5. **Subir y extraer** todos los archivos del proyecto

## Paso 3: Configurar Permisos

Asegúrate de que estos archivos tengan los permisos correctos:
- **Archivos PHP**: 644
- **Carpeta assets**: 755
- **Archivo .htaccess**: 644

## Paso 4: Agregar Imágenes

Sube estas imágenes a la carpeta `assets/images/`:

### ✅ Imágenes requeridas:
- `logo_escala_libre.jpg` ✓ (ya tienes)
- `hero-children-learning.jpg` - **Ni<PERSON>s aprendiendo felices**
- `community-together.jpg` - **Familias y educadores**
- `daily-activities.jpg` - **Actividades diarias**
- `children-art-activities.jpg` - **Niños en arte/música**
- `certification-diploma.jpg` - **Certificación/diploma**
- `favicon.ico` - **Icono del sitio**

### 📸 Sugerencias específicas:

**hero-children-learning.jpg** (500x400px aprox.)
- Niños de 5-12 años aprendiendo al aire libre
- Ambiente natural, colores cálidos
- Expresiones de alegría y curiosidad

**community-together.jpg** (400x300px aprox.)
- Padres, niños y educadores interactuando
- Ambiente acogedor, familiar
- Diversidad de edades

**daily-activities.jpg** (400x300px aprox.)
- Niños en círculo, talleres o juegos
- Materiales educativos visibles
- Ambiente organizado pero libre

**children-art-activities.jpg** (400x300px aprox.)
- Niños pintando, tocando instrumentos o actuando
- Colores vibrantes, creatividad
- Expresión artística

**certification-diploma.jpg** (300x250px aprox.)
- Diploma, certificado o graduación
- Colores institucionales
- Aspecto profesional

## Paso 5: Configurar Email

### Opción A: Email básico (más simple)
- Verifica que tu hosting soporte `mail()`
- El formulario funcionará automáticamente

### Opción B: SMTP (más confiable)
- Configura las variables SMTP en `.env`
- Instala PHPMailer (futuro)

## Paso 6: Personalizar Información

Edita `config.php` para cambiar:
- URL del sitio
- Email de contacto
- Teléfono
- Información específica

## Paso 7: Probar el Sitio

1. **Visita tu dominio**
2. **Verifica navegación** en móvil y desktop
3. **Prueba el formulario** de contacto
4. **Revisa que las imágenes** se muestren correctamente

## 🔧 Solución de Problemas

### Error 500 - Internal Server Error
- Verifica permisos de archivos
- Revisa que mod_rewrite esté habilitado
- Comprueba la sintaxis de .htaccess

### Imágenes no se muestran
- Verifica que las imágenes estén en `assets/images/`
- Comprueba los nombres de archivo (case-sensitive)
- Asegúrate de que los permisos sean correctos

### Formulario no envía emails
- Verifica que la función `mail()` esté habilitada
- Comprueba la configuración de email del servidor
- Revisa los logs de error de PHP

### Sitio no responsive
- Verifica que el CSS se esté cargando
- Comprueba la etiqueta viewport en el header
- Revisa la consola del navegador por errores

## 📱 Optimizaciones Adicionales

### Para mejor rendimiento:
1. **Optimizar imágenes**: Usar WebP cuando sea posible
2. **Comprimir CSS/JS**: Minificar archivos
3. **CDN**: Considerar usar un CDN para assets
4. **Cache**: Configurar cache del navegador

### Para mejor SEO:
1. **Google Analytics**: Agregar código de seguimiento
2. **Search Console**: Registrar el sitio
3. **Schema.org**: Agregar datos estructurados
4. **SSL**: Asegurar que HTTPS esté habilitado

## 🆘 Soporte

Si tienes problemas:
1. **Revisa los logs** de error de PHP
2. **Verifica la consola** del navegador
3. **Comprueba la configuración** del hosting
4. **Contacta al soporte** de tu proveedor de hosting

## ✅ Checklist Final

- [ ] Archivos subidos correctamente
- [ ] Permisos configurados
- [ ] Imágenes agregadas
- [ ] Sitio accesible desde el navegador
- [ ] Navegación funciona en móvil
- [ ] Formulario de contacto funciona
- [ ] URLs limpias funcionan
- [ ] Meta tags configurados
- [ ] Información de contacto actualizada

¡Tu sitio de Escala Libre está listo! 🎉
