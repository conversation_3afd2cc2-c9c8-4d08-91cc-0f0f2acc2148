/* ===== VARIABLES CSS ===== */
:root {
    /* Colores del brand */
    --color-verde-petroleo: #007C84;
    --color-amarillo-mostaza: #F4C843;
    --color-turquesa-claro: #00B1AE;
    --color-magenta-oscuro: #C03671;
    --color-verde-oscuro: #005D64;
    --color-blanco-crema: #FAF8F4;
    
    /* Colores adicionales */
    --color-white: #ffffff;
    --color-black: #333333;
    --color-gray-light: #f8f9fa;
    --color-gray: #6c757d;
    --color-gray-dark: #495057;
    
    /* Tipografía */
    --font-primary: 'Poppins', sans-serif;
    --font-secondary: 'Playfair Display', serif;
    
    /* Tamaños de fuente */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Espaciado */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Sombras */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Bordes */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;
    
    /* Transiciones */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ===== RESET Y BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--color-black);
    background-color: var(--color-blanco-crema);
    overflow-x: hidden;
}

/* ===== TIPOGRAFÍA ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--color-verde-petroleo);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--color-gray-dark);
}

a {
    color: var(--color-turquesa-claro);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--color-verde-petroleo);
    text-decoration: underline;
}

/* ===== UTILIDADES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--border-radius-lg);
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background-color: var(--color-turquesa-claro);
    color: var(--color-white);
}

.btn-primary:hover {
    background-color: var(--color-verde-petroleo);
    color: var(--color-white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: var(--color-amarillo-mostaza);
    color: var(--color-verde-oscuro);
}

.btn-secondary:hover {
    background-color: var(--color-magenta-oscuro);
    color: var(--color-white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* ===== BRAND STYLING ===== */
.brand-name .escala {
    color: var(--color-verde-petroleo);
    font-weight: 700;
}

.brand-name .libre {
    color: var(--color-amarillo-mostaza);
    font-weight: 500;
}

/* ===== SECCIONES ===== */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--color-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== CARDS ===== */
.card {
    background: var(--color-white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    height: 100%;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.card-title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.card-text {
    color: var(--color-gray-dark);
    text-align: center;
    line-height: 1.7;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    :root {
        --font-size-5xl: 2.25rem;
        --font-size-4xl: 1.875rem;
        --font-size-3xl: 1.5rem;
    }
    
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .section-header {
        margin-bottom: var(--spacing-2xl);
    }
    
    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
}

/* ===== NAVBAR ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(250, 248, 244, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 124, 132, 0.1);
    z-index: var(--z-fixed);
    transition: var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(250, 248, 244, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.nav-logo a {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    text-decoration: none;
}

.logo-img {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-lg);
    object-fit: cover;
}

.logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1;
}

.logo-text .escala {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--color-verde-petroleo);
}

.logo-text .libre {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--color-amarillo-mostaza);
}

.nav-menu {
    display: flex;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
    margin: 0;
    padding: 0;
}

.nav-link {
    color: var(--color-verde-petroleo);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
}

.nav-link:hover {
    background-color: var(--color-turquesa-claro);
    color: var(--color-white);
    text-decoration: none;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--color-verde-petroleo);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--color-blanco-crema) 0%, rgba(0, 177, 174, 0.1) 100%);
    padding-top: 80px;
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    width: 100%;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.hero-text {
    z-index: 2;
}

.hero-title {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--spacing-lg);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--font-size-2xl);
    color: var(--color-turquesa-claro);
    margin-bottom: var(--spacing-lg);
    font-weight: 500;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--color-gray-dark);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.hero-image {
    position: relative;
    z-index: 1;
}

.hero-img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
}

.hero-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-element {
    position: absolute;
    font-size: var(--font-size-3xl);
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    top: 20%;
    right: 15%;
    animation-delay: 1.5s;
}

.floating-element:nth-child(3) {
    bottom: 30%;
    left: 15%;
    animation-delay: 3s;
}

.floating-element:nth-child(4) {
    bottom: 15%;
    right: 10%;
    animation-delay: 4.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
}

.hero-wave svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: 120px;
}

/* ===== ABOUT SECTION ===== */
.about {
    padding: var(--spacing-3xl) 0;
    background: var(--color-white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    margin-bottom: var(--spacing-3xl);
}

.about-description {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    margin-bottom: var(--spacing-lg);
}

.about-img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
}

.mission-vision {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
}

/* ===== SERVICES SECTION ===== */
.services {
    padding: var(--spacing-3xl) 0;
    background: var(--color-gray-light);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-2xl);
}

.service-card {
    background: var(--color-white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.service-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
}

.service-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--color-verde-petroleo);
}

.service-description {
    color: var(--color-gray-dark);
    line-height: 1.7;
}

/* ===== SCHEDULE SECTION ===== */
.schedule {
    padding: var(--spacing-3xl) 0;
    background: var(--color-white);
}

.schedule-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.schedule-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.schedule-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--color-gray-light);
    border-radius: var(--border-radius-lg);
}

.schedule-icon {
    font-size: var(--font-size-3xl);
    flex-shrink: 0;
}

.schedule-text h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
    color: var(--color-verde-petroleo);
}

.schedule-text p {
    color: var(--color-gray-dark);
    margin: 0;
}

.schedule-img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
}

/* ===== WHY CHOOSE SECTION ===== */
.why-choose {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--color-turquesa-claro), var(--color-verde-petroleo));
    color: var(--color-white);
}

.why-choose .section-title {
    color: var(--color-white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
}

.feature-icon {
    font-size: var(--font-size-xl);
    color: var(--color-amarillo-mostaza);
    flex-shrink: 0;
}

.feature-item p {
    color: var(--color-white);
    margin: 0;
    font-weight: 500;
}

/* ===== METHODOLOGY SECTION ===== */
.methodology {
    padding: var(--spacing-3xl) 0;
    background: var(--color-gray-light);
}

.methodology-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.methodology-text h3 {
    color: var(--color-magenta-oscuro);
    margin-bottom: var(--spacing-lg);
}

.art-list {
    list-style: none;
    padding: 0;
    margin: var(--spacing-lg) 0;
}

.art-list li {
    padding: var(--spacing-sm) 0;
    font-size: var(--font-size-lg);
    color: var(--color-gray-dark);
}

.highlight {
    background: linear-gradient(120deg, var(--color-amarillo-mostaza) 0%, var(--color-amarillo-mostaza) 100%);
    background-repeat: no-repeat;
    background-size: 100% 0.2em;
    background-position: 0 88%;
    padding: var(--spacing-sm) 0;
    font-weight: 600;
    color: var(--color-verde-oscuro);
}

.methodology-img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
}

/* ===== CERTIFICATION SECTION ===== */
.certification {
    padding: var(--spacing-3xl) 0;
    background: var(--color-white);
}

.certification-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.certification-text p {
    font-size: var(--font-size-lg);
    line-height: 1.8;
}

.cert-img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: var(--spacing-3xl) 0;
    background: var(--color-gray-light);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.contact-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--color-turquesa-claro);
    color: var(--color-white);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.contact-text h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
    color: var(--color-verde-petroleo);
}

.contact-text a,
.contact-text p {
    color: var(--color-gray-dark);
    margin: 0;
}

.contact-text a:hover {
    color: var(--color-turquesa-claro);
}

/* ===== FORM STYLES ===== */
.form {
    background: var(--color-white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--color-verde-petroleo);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--color-gray-light);
    border-radius: var(--border-radius-md);
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-turquesa-claro);
    box-shadow: 0 0 0 3px rgba(0, 177, 174, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--color-verde-oscuro);
    color: var(--color-white);
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-section h3 {
    color: var(--color-amarillo-mostaza);
    margin-bottom: var(--spacing-lg);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.footer-logo-img {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-md);
    object-fit: cover;
}

.footer-logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1;
}

.footer-logo-text .escala {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--color-white);
}

.footer-logo-text .libre {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--color-amarillo-mostaza);
}

.footer-tagline {
    font-style: italic;
    color: var(--color-turquesa-claro);
    margin-bottom: var(--spacing-md);
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--color-turquesa-claro);
    text-decoration: none;
}

.footer-contact {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.contact-item i {
    color: var(--color-turquesa-claro);
    width: 20px;
}

.contact-item a,
.contact-item span {
    color: rgba(255, 255, 255, 0.8);
}

.contact-item a:hover {
    color: var(--color-turquesa-claro);
    text-decoration: none;
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    color: var(--color-white);
    border-radius: var(--border-radius-full);
    transition: var(--transition-fast);
}

.social-link:hover {
    background: var(--color-turquesa-claro);
    color: var(--color-white);
    text-decoration: none;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
    text-align: center;
}

.footer-copyright p {
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: var(--spacing-sm);
}

.footer-love {
    color: rgba(255, 255, 255, 0.8);
}

.footer-love i {
    color: var(--color-magenta-oscuro);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet */
@media (max-width: 1024px) {
    .hero-content,
    .about-content,
    .schedule-content,
    .methodology-content,
    .certification-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .hero-image,
    .about-image,
    .schedule-image,
    .methodology-image {
        order: -1;
    }

    .mission-vision {
        grid-template-columns: 1fr;
    }

    .hero-buttons {
        justify-content: center;
    }
}

/* Mobile */
@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--color-blanco-crema);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: var(--spacing-2xl);
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-list {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .nav-link {
        font-size: var(--font-size-lg);
        padding: var(--spacing-md) var(--spacing-xl);
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }

    /* Hero */
    .hero {
        min-height: 80vh;
        text-align: center;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-img {
        height: 300px;
    }

    /* Sections */
    .services-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .schedule-info {
        gap: var(--spacing-lg);
    }

    .schedule-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    /* Images */
    .hero-img,
    .about-img,
    .schedule-img,
    .methodology-img,
    .cert-img {
        height: 250px;
    }

    /* Contact */
    .contact-content {
        grid-template-columns: 1fr;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-logo {
        justify-content: center;
    }

    .social-links {
        justify-content: center;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .nav-container {
        padding: 0 var(--spacing-sm);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-xl);
    }

    .section-title {
        font-size: var(--font-size-3xl);
    }

    .card,
    .service-card,
    .form {
        padding: var(--spacing-lg);
    }

    .hero-buttons {
        width: 100%;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}
