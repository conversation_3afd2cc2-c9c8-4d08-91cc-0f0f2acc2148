<?php
require_once 'config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

// Function to sanitize input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to validate email
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Initialize response
$response = ['success' => false, 'message' => ''];

try {
    // Get and sanitize form data
    $name = isset($_POST['name']) ? sanitize_input($_POST['name']) : '';
    $email = isset($_POST['email']) ? sanitize_input($_POST['email']) : '';
    $phone = isset($_POST['phone']) ? sanitize_input($_POST['phone']) : '';
    $message = isset($_POST['message']) ? sanitize_input($_POST['message']) : '';
    
    // Validation
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'El nombre es requerido';
    } elseif (strlen($name) < 2) {
        $errors[] = 'El nombre debe tener al menos 2 caracteres';
    }
    
    if (empty($email)) {
        $errors[] = 'El email es requerido';
    } elseif (!validate_email($email)) {
        $errors[] = 'El email no es válido';
    }
    
    if (empty($message)) {
        $errors[] = 'El mensaje es requerido';
    } elseif (strlen($message) < 10) {
        $errors[] = 'El mensaje debe tener al menos 10 caracteres';
    }
    
    // If there are validation errors
    if (!empty($errors)) {
        $response['message'] = implode(', ', $errors);
        echo json_encode($response);
        exit;
    }
    
    // Prepare email content
    $to = SITE_EMAIL;
    $subject = 'Nuevo contacto desde ' . SITE_NAME;
    
    // Email body
    $email_body = "
    <html>
    <head>
        <title>Nuevo contacto desde " . SITE_NAME . "</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #007C84; color: white; padding: 20px; text-align: center; }
            .content { background-color: #f9f9f9; padding: 20px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #007C84; }
            .value { margin-top: 5px; }
            .footer { background-color: #005D64; color: white; padding: 15px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>Nuevo contacto desde " . SITE_NAME . "</h2>
            </div>
            <div class='content'>
                <div class='field'>
                    <div class='label'>Nombre:</div>
                    <div class='value'>" . htmlspecialchars($name) . "</div>
                </div>
                <div class='field'>
                    <div class='label'>Email:</div>
                    <div class='value'>" . htmlspecialchars($email) . "</div>
                </div>
                <div class='field'>
                    <div class='label'>Teléfono:</div>
                    <div class='value'>" . (empty($phone) ? 'No proporcionado' : htmlspecialchars($phone)) . "</div>
                </div>
                <div class='field'>
                    <div class='label'>Mensaje:</div>
                    <div class='value'>" . nl2br(htmlspecialchars($message)) . "</div>
                </div>
                <div class='field'>
                    <div class='label'>Fecha y hora:</div>
                    <div class='value'>" . date('d/m/Y H:i:s') . "</div>
                </div>
                <div class='field'>
                    <div class='label'>IP del visitante:</div>
                    <div class='value'>" . $_SERVER['REMOTE_ADDR'] . "</div>
                </div>
            </div>
            <div class='footer'>
                <p>Este mensaje fue enviado desde el formulario de contacto de " . SITE_NAME . "</p>
                <p>Sitio web: " . SITE_URL . "</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    // Email headers
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . SITE_NAME . ' <noreply@' . parse_url(SITE_URL, PHP_URL_HOST) . '>',
        'Reply-To: ' . $email,
        'X-Mailer: PHP/' . phpversion()
    ];
    
    // Send email
    $mail_sent = mail($to, $subject, $email_body, implode("\r\n", $headers));
    
    if ($mail_sent) {
        // Log the contact (optional - for future database integration)
        $log_entry = date('Y-m-d H:i:s') . " - Contacto de: $name ($email) - Mensaje: " . substr($message, 0, 100) . "...\n";
        file_put_contents('contacts.log', $log_entry, FILE_APPEND | LOCK_EX);
        
        $response['success'] = true;
        $response['message'] = '¡Mensaje enviado correctamente! Te contactaremos pronto.';
    } else {
        $response['message'] = 'Error al enviar el mensaje. Por favor, inténtalo de nuevo o contáctanos directamente por WhatsApp.';
    }
    
} catch (Exception $e) {
    if (DEBUG) {
        $response['message'] = 'Error: ' . $e->getMessage();
    } else {
        $response['message'] = 'Error interno del servidor. Por favor, inténtalo de nuevo más tarde.';
    }
    
    // Log error
    error_log('Contact form error: ' . $e->getMessage());
}

// Send response
echo json_encode($response);
?>
