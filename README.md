# Escala Libre - Landing Page

Landing page para Escala Libre, una comunidad educativa homeschool presencial en Lima, Perú.

## 🌟 Características

- **Diseño Responsive**: Adaptable a dispositivos móviles, tablets y desktop
- **PHP Puro**: Sin frameworks, optimizado para cPanel
- **Modular**: Componentes separados para fácil mantenimiento
- **SEO Optimizado**: Meta tags, Open Graph, estructura semántica
- **Formulario de Contacto**: Con validación y envío por email
- **Animaciones Suaves**: Efectos visuales atractivos
- **URLs Limpias**: Configuración .htaccess incluida

## 🎨 Paleta de Colores

1. **Verde petróleo** `#007C84` - Texto "ESCALA", elementos principales
2. **Amarillo mostaza** `#F4C843` - Texto "Libre", acentos
3. **Turquesa claro** `#00B1AE` - Bo<PERSON>, enlaces
4. **Magenta oscuro** `#C03671` - Detalles, hover states
5. **Verde oscuro** `#005D64` - <PERSON>er, elementos secundarios
6. **Blanco crema** `#FAF8F4` - Fondo principal

## 📁 Estructura del Proyecto

```
escalalibre/
├── assets/
│   ├── css/
│   │   └── styles.css          # Estilos principales
│   ├── js/
│   │   └── main.js             # JavaScript funcional
│   └── images/                 # Imágenes del sitio
├── components/
│   ├── header.php              # Cabecera y navegación
│   └── footer.php              # Pie de página
├── .htaccess                   # Configuración Apache
├── config.php                  # Configuración general
├── index.php                   # Página principal
├── contact.php                 # Procesador de formulario
├── .env.example                # Variables de entorno ejemplo
└── README.md                   # Este archivo
```

## 🚀 Instalación

1. **Subir archivos**: Sube todos los archivos a tu servidor cPanel
2. **Configurar dominio**: Asegúrate de que el dominio apunte a la carpeta del proyecto
3. **Agregar imágenes**: Coloca las imágenes necesarias en `assets/images/`
4. **Configurar email**: Verifica que el servidor soporte la función `mail()` de PHP

## 📸 Imágenes Necesarias

Coloca estas imágenes en la carpeta `assets/images/`:

- `logo_escala_libre.jpg` - Logo principal (proporcionado)
- `hero-children-learning.jpg` - Niños aprendiendo en comunidad (hero section)
- `community-together.jpg` - Comunidad, maestros y niños juntos
- `daily-activities.jpg` - Niños en actividades diarias
- `children-art-activities.jpg` - Niños haciendo arte, música o teatro
- `certification-diploma.jpg` - Imagen relacionada con certificación
- `favicon.ico` - Icono del sitio

### Sugerencias para las imágenes:
- **Hero**: Niños felices aprendiendo al aire libre o en espacios creativos
- **Comunidad**: Familias y educadores interactuando
- **Actividades**: Niños pintando, tocando instrumentos, jugando
- **Certificación**: Diploma, graduación o reconocimiento académico

## ⚙️ Configuración

### Variables de Entorno (Opcional)
Copia `.env.example` a `.env` y configura las variables según tu entorno.

### Personalización
- Modifica `config.php` para cambiar información del sitio
- Edita `assets/css/styles.css` para ajustar estilos
- Actualiza `components/header.php` y `components/footer.php` para cambios estructurales

## 📧 Formulario de Contacto

El formulario envía emails usando la función `mail()` de PHP. Asegúrate de que:
- Tu servidor soporte envío de emails
- El dominio tenga configuración SPF/DKIM (recomendado)
- Para mejor deliverability, considera usar PHPMailer con SMTP

## 🔧 Funcionalidades Futuras

El proyecto está preparado para:
- **Base de datos**: Configuración lista en `config.php`
- **Panel de administración**: Estructura modular facilita la expansión
- **Sistema de usuarios**: Padres y profesores
- **Calendario de actividades**
- **Sistema de mensajería**
- **Notificaciones**

## 📱 Responsive Design

El sitio es completamente responsive con breakpoints:
- **Desktop**: > 1024px
- **Tablet**: 768px - 1024px
- **Mobile**: < 768px
- **Small Mobile**: < 480px

## 🌐 SEO y Performance

- Meta tags optimizados
- Open Graph para redes sociales
- Compresión gzip habilitada
- Cache de archivos estáticos
- Imágenes optimizadas (recomendado WebP)
- Lazy loading implementado

## 📞 Contacto

- **Email**: <EMAIL>
- **WhatsApp**: +51 994 953 176
- **Ubicación**: Lima, Perú

## 📄 Licencia

Este proyecto es propiedad de Escala Libre. Todos los derechos reservados.

---

**Desarrollado con ❤️ para transformar la educación en el Perú**
